from app.interfaces.external.photo_service import IPhotoService
from app.core.config import settings
import requests
from typing import List, Dict, Any
import urllib.parse

class SimfoniService(IPhotoService):
    def __init__(self):
        self.api_key = settings.SIMFONI_API_KEY
        self.base_url = settings.SIMFONI_BASE_URL
        print(f'simfoni baseUrl {self.base_url}')

    def fetch_profiles(self, max = None) -> List[Dict[str, Any]]:
        # Implementation to fetch profiles from Simfoni service
        print("Fetching profiles from Simfoni service")

        profiles = []
        index = 1
        failure = 0

        #TODO: fetch last-id

        while True:   
            result = self.get_profile_by_id(f"FP{(index):05d}")
            if len(result) == 0:
                    print('Failed Get profile: ', index) 
                    failure += 1
                    if failure >= 5:
                        print('got only profile: ', len(profiles))
                        break 
                    index += 1
                    continue 
                    
            profiles.append(result)
            index += 1
            failure = 0
            
            if len(profiles) % 100 == 0:
                    print(f'>> Got {len(profiles)} profiles...')
            if max is not None and len(profiles) >= max:
                print(f'-- return max profile: {len(profiles)}')
                return profiles

        return profiles

    def fetch_photos(self, max = None) -> List[Dict[str, Any]]:
        # Implementation to fetch photos from Simfoni service
        print("Fetching photos from Simfoni service")
        photos = []
        index = 1
        failure = 0

        while True :   
            photo = self.get_photo_by_id(f"FP{(index):05d}")
            if len(photo) == 0:
                print('Failed Get Photo: ', index) 
                failure += 1
                if failure >= 5:
                    print('got only photo: ', len(photos)) 
                    break
                index += 1
                continue 
                
            photos.append(photo)
            index += 1
            failure = 0
            if len(photos) % 100 == 0:
                    print(f'>> Got {len(photos)} photos...')
                    
            #return if max set
            if max is not None and len(photos) >= max:
                print(f'-- return max photo: {len(photos)}')
                return photos

        return photos

    def get_photo_by_id(self, id):
        query = {'id': id}
        header = {'Key': self.api_key}
        resp = requests.get(f'{self.base_url}/accepted', params = query, headers = header)
        if resp.status_code == 200:
            return resp.json()['simfoni']
        else:
            print(f'err... {resp.status_code}')
            query = {'id': id}
            return {}

    def get_profile_by_id(self, id):
        query = {'id': id}
        header = {'Key': self.api_key}
        resp = requests.get(f'{self.base_url}/member', params = query, headers = header)
        if resp.status_code == 200:
            return resp.json()['simfoni']
        else:
            print(f'err... {resp.status_code}')
            query = {'id': id}
            return {}

    def _get_headers(self) -> Dict[str, str]:
        return {"Authorization": f"Bearer {self.api_key}"}

    def construct_url(self, photo: Dict[str, Any]) -> str:
        """Helper method to construct photo URL"""
        if 'url' in photo and photo['url'].startswith('https://'):
            return photo['url']
        return f"https://simfoni.fpsi.or.id/data/accepted_photo/{photo['folder']}/{urllib.parse.quote(photo['filename'])}" 