from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # API Settings
    APP_NAME: str = "Image Similarity API"
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"
    
    # Vector Database Settings (Milvus)
    MILVUS_HOST: str = "localhost"
    MILVUS_URI: str = "localhost"
    MILVUS_TOKEN: str = "localhost"
    MILVUS_PORT: int = 19530
    MILVUS_COLLECTION: str = "image_vectors"
    
    # Redis Settings
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0

    MONGODB_URI: str = "localhost"
    
    # Image Processing
    MIN_SIMILARITY_SCORE: float = 0.7
    FEATURE_VECTOR_DIM: int = 512  # Dimension of your feature vectors
    MAX_BATCH_SIZE: int = 100

    # Model
    FEATURE_EXTRACTION_MODEL: str = "mobilenet"
    VECTOR_COLLECTION_DIMENSION: int = 1280
    
    # Security
    PUBLIC_KEY_HEADER: str = "public-key"
    API_KEY_REQUIRED: bool = True
    
    # Background Tasks
    ENABLE_BACKGROUND_TASKS: bool = True
    MAX_BACKGROUND_WORKERS: int = 3

    SIMFONI_API_KEY: str = "xxx"
    SIMFONI_BASE_URL: str = "https://api.simfoni-fpsi.web.id"
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"

settings = Settings() 