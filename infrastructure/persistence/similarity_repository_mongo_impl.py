from typing import Optional, Dict, Any, List
import pymongo
from bson.objectid import ObjectId
from domain.entities.photo import Photo, Event, User
from domain.repositories.similarity_repository import SimilarityRepository
import re
from pymongo import UpdateOne
import logging
import time


class SimilarityRepoMongo(SimilarityRepository):
    def __init__(self, mongo_client):
        self.db = mongo_client['similarity']
        self.events_collection = self.db['events']
        self.images_collection = self.db['images']
        self.users_collection = self.db['users']
        logging.info('init repo mongo...')

    # def fetch_image_detail(self, url) -> Optional[Photo]:
    #     image_data = self.images_collection.find_one({'url': url})
    #     if image_data:
    #         user_data = self.users_collection.find_one({'_id': ObjectId(image_data['user_id'])})
    #         if user_data:
    #             event_data = self.events_collection.find_one({'_id': ObjectId(image_data['event_id'])})
    #             if event_data:
    #                 event = Event(
    #                     year=event_data['year'],
    #                     name=event_data['event_name'],
    #                     short_name=event_data['event_shortname']
    #                 )

    #                 user = User(
    #                     name=user_data['nama_lengkap'],
    #                     honors=user_data['gelar_fotografi'],
    #                     club=user_data['klub'],
    #                     fp_id=user_data['id'],
    #                     email=user_data['email']
    #                 )

    #                 photo_data = Photo(
    #                     title=image_data['title'],
    #                     category=image_data['category'],
    #                     event=event,
    #                     user=user
    #                 ).to_dict()
    #                 return photo_data
    #             else: print(f'event not found "{image_data["event_id"]}" | {image_data}')
    #         else: print(f'user not found')    
    #     else: print('image not found')        
    #     return None

    def get_event(self,  event_id) -> Optional[Event]:
        event_data = self.events_collection.find_one({'_id': ObjectId(event_id)})
        if event_data:
            return Event(
                year=event_data['year'],
                name=event_data['event_name'],
                short_name=event_data['event_shortname']
            )
        else:
            logging.info(f'event not found "{event_id}"')
            return None

    def get_user(self, user_id) -> Optional[User]:
        user_data = self.users_collection.find_one({'_id': ObjectId(user_id)})
        if user_data:
            return User(
                name=user_data['nama_lengkap'],
                honors=user_data['gelar_fotografi'],
                club=user_data['klub'],
                fp_id=user_data['id'],
                email=user_data['email']
            )
        else:
            logging.info(f'user not found "{user_id}"')
            return None

    def fetch_image_detail(self, url) -> Optional[Dict[str, Any]]:
        image_data = self.images_collection.find_one({'url': url})
        if image_data:
            event = self.get_event(image_data['event_id'])
            user = self.get_user(image_data['user_id'])

            photo_data = Photo(
                id=image_data.get('id', None),
                title=image_data['title'],
                category=image_data['category'],
                cluster_id=image_data.get('cluster_id', 0),                
                event=event,
                user=user,
                meta_data=image_data.get('meta_data')  # Get meta_data if it exists
            ).to_dict()

            # If event or user is None, remove it from the photo data
            if event is None:
                logging.info(f'event not found: {image_data["event_id"]}, url: {url}')
                del photo_data['event']
            if user is None:
                del photo_data['user']

            return photo_data
        else:
            print('image not found')
            return None

    def fetch_image_by_index(self, index) -> Optional[str]:
        image_data = self.images_collection.find_one({}, skip=index)
        if image_data:
            return image_data['url']
        return None

    def search_user(self, name: str):
        query = {'nama_lengkap': re.compile(name, re.IGNORECASE)}
        users = self.users_collection.find(query)

        results = []
        for user in users:
            print('user found...', user['nama_lengkap'])
            user_data = {
                'id': str(user['_id']),
                'name': user['nama_lengkap'],
                'honors': user['gelar_fotografi'],
                'club': user['klub'],
                'email': user['email'],
                'images': []
            }

            # Find images for the user
            image_query = {'user_id': user['_id']}
            images = self.images_collection.find(image_query)
            for image in images:
                event = self.events_collection.find_one({'_id': image['event_id']})
                event_data = {
                    'year': event['year'],
                    'name': event['event_name'],
                    'short_name': event['event_shortname']
                }

                image_data = {
                    'title': image['title'],
                    'category': image['category'],
                    'event': event_data
                }
                user_data['images'].append(image_data)

            results.append(user_data)

        return results

    async def batch_insert_photos(self, photos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Insert multiple photos in a batch operation.
        
        Args:
            photos: List of photo data dictionaries to insert
            
        Returns:
            Dict containing operation results and statistics
        """
        successful = 0
        failed = 0
        failed_details = []
        
        try:
            # Start a session for transaction support
            async with await self.db_client.start_session() as session:
                async with session.start_transaction():
                    for photo in photos:
                        try:
                            # Validate required fields
                            required_fields = ['id', 'url', 'features']
                            if not all(field in photo for field in required_fields):
                                raise ValueError(f"Missing required fields: {required_fields}")

                            # Add created_at timestamp
                            photo['created_at'] = int(time.time() * 1000)

                            # Insert the photo
                            result = await self.images_collection.insert_one(photo, session=session)
                            if result.inserted_id:
                                successful += 1
                            else:
                                raise ValueError("Failed to insert photo")
                                
                        except Exception as e:
                            failed += 1
                            failed_details.append({
                                'photo_id': photo.get('id', 'unknown'),
                                'error': str(e)
                            })
                            # Continue with next photo even if one fails
                            continue
                            
            return {
                'status': 'success' if failed == 0 else 'partial',
                'message': 'All photos inserted successfully' if failed == 0 else 'Some photos failed to insert',
                'details': {
                    'total_processed': len(photos),
                    'successful': successful,
                    'failed': failed,
                    'failed_details': failed_details
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Batch insert failed: {str(e)}',
                'details': {
                    'total_processed': len(photos),
                    'successful': successful,
                    'failed': failed,
                    'failed_details': failed_details
                }
            }

    def update_batch(self, params):
        current_time = int(time.time() * 1000)
        operations = [
            UpdateOne(
                {"url": url},  # The filter to find the document
                {"$set": {**set, "updated_at": current_time}}  # The update to apply with timestamp
            )
            for url, set in params
        ]

        # 2. Execute all operations in a single batch if the list is not empty
        if operations:
            try:
                result = self.images_collection.bulk_write(operations)
                # The result object contains stats about the operation
                logging.info(f"Bulk update successful, doc matched {result.matched_count}, modified: {result.modified_count}")
            except Exception as e:
                logging.info(f"An error occurred during the bulk write: {e}")
        else:
            logging.info("No operations to perform.")

    def fetch_clusters(self, min_photos: Optional[int] = None, max_photos: Optional[int] = None,
                      sort_by: str = "total_photos", sort_order: str = "desc",
                      limit: Optional[int] = None, max_images_per_cluster: Optional[int] = 100) -> List[Dict[str, Any]]:
        """
        Fetch cluster data grouped by cluster_id using MongoDB aggregation.
        """
        pipeline = []

        # Match documents that have cluster_id
        match_stage = {"cluster_id": {"$exists": True, "$ne": None}}
        pipeline.append({"$match": match_stage})

        # Group by cluster_id and collect limited images to prevent memory issues
        if max_images_per_cluster and max_images_per_cluster > 0:
            # Use $slice to limit images per cluster
            group_stage = {
                "$group": {
                    "_id": "$cluster_id",
                    "all_images": {
                        "$push": {
                            "url": "$url",
                            "title": "$title",
                            "category": "$category",
                            "id": "$id",
                            "event_id": "$event_id",
                            "user_id": "$user_id",
                            "meta_data": "$meta_data"
                        }
                    },
                    "total_images": {"$sum": 1}
                }
            }
            pipeline.append(group_stage)

            # Add projection to slice the images array
            pipeline.append({
                "$project": {
                    "_id": 1,
                    "total_images": 1,
                    "images": {"$slice": ["$all_images", max_images_per_cluster]}
                }
            })
        else:
            # Original grouping without limit
            group_stage = {
                "$group": {
                    "_id": "$cluster_id",
                    "images": {
                        "$push": {
                            "url": "$url",
                            "title": "$title",
                            "category": "$category",
                            "id": "$id",
                            "event_id": "$event_id",
                            "user_id": "$user_id",
                            "meta_data": "$meta_data"
                        }
                    },
                    "total_images": {"$sum": 1}
                }
            }
            pipeline.append(group_stage)

        # Filter by min/max photos if specified
        if min_photos is not None or max_photos is not None:
            match_filter = {}
            if min_photos is not None:
                match_filter["total_images"] = {"$gte": min_photos}
            if max_photos is not None:
                if "total_images" in match_filter:
                    match_filter["total_images"]["$lte"] = max_photos
                else:
                    match_filter["total_images"] = {"$lte": max_photos}
            pipeline.append({"$match": match_filter})

        # Sort by specified field and order
        sort_direction = 1 if sort_order.lower() == "asc" else -1
        if sort_by == "total_photos":
            pipeline.append({"$sort": {"total_images": sort_direction}})
        else:
            pipeline.append({"$sort": {"_id": sort_direction}})

        # Limit results if specified
        if limit is not None:
            pipeline.append({"$limit": limit})

        # Execute aggregation with allowDiskUse to handle large datasets
        clusters = list(self.images_collection.aggregate(pipeline, allowDiskUse=True))

        # Process results and populate user/event data
        result = []
        for cluster in clusters:
            cluster_data = {
                "cluster_id": cluster["_id"],
                "total_images": cluster["total_images"],
                "images": []
            }

            # Process each image in the cluster
            for image in cluster["images"]:
                image_data = {
                    "url": image["url"],
                    "title": image.get("title"),
                    "category": image.get("category"),
                    "id": image.get("id"),
                    "meta_data": image.get("meta_data")
                }

                # Add event data if available
                if image.get("event_id"):
                    event = self.get_event(image["event_id"])
                    if event:
                        image_data["event"] = {
                            "year": event.year,
                            "name": event.name,
                            "short_name": event.short_name
                        }

                # Add user data if available
                if image.get("user_id"):
                    user = self.get_user(image["user_id"])
                    if user:
                        image_data["user"] = {
                            "name": user.name,
                            "email": user.email,
                            "honors": user.honors,
                            "club": user.club,
                            "fp_id": user.fp_id
                        }

                cluster_data["images"].append(image_data)

            result.append(cluster_data)

        return result

    def find_image_cluster(self, image_url: str) -> Optional[Dict[str, Any]]:
        """
        Find which cluster an image belongs to.
        """
        # Find the image document
        image_data = self.images_collection.find_one({'url': image_url})
        if not image_data or 'cluster_id' not in image_data:
            return None

        cluster_id = image_data['cluster_id']

        # Get all images in the same cluster
        cluster_images = list(self.images_collection.find({'cluster_id': cluster_id}))

        cluster_data = {
            "cluster_id": cluster_id,
            "total_images": len(cluster_images),
            "images": []
        }

        # Process each image in the cluster
        for image in cluster_images:
            image_info = {
                "url": image["url"],
                "title": image.get("title"),
                "category": image.get("category"),
                "id": image.get("id"),
                "meta_data": image.get("meta_data")
            }

            # Add event data if available
            if image.get("event_id"):
                event = self.get_event(image["event_id"])
                if event:
                    image_info["event"] = {
                        "year": event.year,
                        "name": event.name,
                        "short_name": event.short_name
                    }

            # Add user data if available
            if image.get("user_id"):
                user = self.get_user(image["user_id"])
                if user:
                    image_info["user"] = {
                        "name": user.name,
                        "email": user.email,
                        "honors": user.honors,
                        "club": user.club,
                        "fp_id": user.fp_id
                    }

            cluster_data["images"].append(image_info)

        return cluster_data

    def fetch_clusters_simple(self, min_photos: Optional[int] = None, max_photos: Optional[int] = None,
                             sort_by: str = "total_photos", sort_order: str = "desc",
                             limit: Optional[int] = None, max_images_per_cluster: Optional[int] = 100) -> List[Dict[str, Any]]:
        """
        Fetch cluster data using simple queries instead of aggregation.
        This method reduces MongoDB memory usage by doing the heavy lifting in Python.
        """
        logging.info(f"fetch_clusters_simple: min_photos={min_photos}, max_photos={max_photos}, limit={limit}")

        try:
            # Step 1: Get distinct cluster_ids with basic filtering
            match_filter = {"cluster_id": {"$exists": True, "$ne": None}}

            # Get all distinct cluster IDs
            cluster_ids = self.images_collection.distinct("cluster_id", match_filter)
            logging.info(f"Found {len(cluster_ids)} distinct cluster IDs")

            # Step 2: For each cluster, count images and collect data
            clusters_data = []

            for cluster_id in cluster_ids:
                # Count total images in this cluster
                total_count = self.images_collection.count_documents({"cluster_id": cluster_id})

                # Apply min/max photo filtering
                if min_photos is not None and total_count < min_photos:
                    continue
                if max_photos is not None and total_count > max_photos:
                    continue

                # Get limited images for this cluster
                images_limit = max_images_per_cluster if max_images_per_cluster else 0
                images_cursor = self.images_collection.find(
                    {"cluster_id": cluster_id},
                    {
                        "url": 1,
                        "title": 1,
                        "category": 1,
                        "id": 1,
                        "event_id": 1,
                        "user_id": 1,
                        "meta_data": 1
                    }
                ).limit(images_limit)

                cluster_data = {
                    "cluster_id": cluster_id,
                    "total_images": total_count,
                    "images": []
                }

                # Process each image in the cluster
                for image in images_cursor:
                    image_data = {
                        "url": image["url"],
                        "title": image.get("title"),
                        "category": image.get("category"),
                        "id": image.get("id"),
                        "meta_data": image.get("meta_data")
                    }

                    # Add event data if available
                    if image.get("event_id"):
                        event = self.get_event(image["event_id"])
                        if event:
                            image_data["event"] = {
                                "year": event.year,
                                "name": event.name,
                                "short_name": event.short_name
                            }

                    # Add user data if available
                    if image.get("user_id"):
                        user = self.get_user(image["user_id"])
                        if user:
                            image_data["user"] = {
                                "name": user.name,
                                "email": user.email,
                                "honors": user.honors,
                                "club": user.club,
                                "fp_id": user.fp_id
                            }

                    cluster_data["images"].append(image_data)

                clusters_data.append(cluster_data)

            # Step 3: Sort in Python
            if sort_by == "total_photos":
                reverse = sort_order.lower() == "desc"
                clusters_data.sort(key=lambda x: x["total_images"], reverse=reverse)

            # Step 4: Apply limit
            if limit is not None:
                clusters_data = clusters_data[:limit]

            logging.info(f"Returning {len(clusters_data)} clusters")
            return clusters_data

        except Exception as e:
            logging.error(f"Error in fetch_clusters_simple: {str(e)}")
            raise

    def find_one_without_cluster_id(self) -> Optional[Dict[str, Any]]:
        """
        Find one document that doesn't have a cluster_id assigned.
        """
        try:
            # Find documents where cluster_id doesn't exist or is None
            query = {
                "$or": [
                    {"cluster_id": {"$exists": False}},
                    {"cluster_id": None}
                ]
            }

            document = self.images_collection.find_one(query)
            return document

        except Exception as e:
            logging.error(f"Error in find_one_without_cluster_id: {e}")
            return None

    def get_max_cluster_id(self) -> int:
        """
        Get the maximum cluster_id currently in the database.
        """
        try:
            # Find the document with the highest cluster_id
            result = self.images_collection.find_one(
                {"cluster_id": {"$exists": True, "$ne": None}},
                sort=[("cluster_id", -1)]
            )

            if result and "cluster_id" in result:
                return int(result["cluster_id"])
            else:
                return 0

        except Exception as e:
            logging.error(f"Error in get_max_cluster_id: {e}")
            return 0

    def update_cluster_id(self, url: str, cluster_id: int) -> bool:
        """
        Update the cluster_id for a specific image URL.
        """
        try:
            current_time = int(time.time() * 1000)
            result = self.images_collection.update_many(
                {"url": url},
                {"$set": {"cluster_id": cluster_id, "updated_at": current_time}}
            )

            return result.modified_count > 0

        except Exception as e:
            logging.error(f"Error in update_cluster_id for url {url}: {e}")
            return False
